# Broadcast WhatsApp dari Excel

Sistem web untuk mengirim pesan broadcast WhatsApp berdasarkan data dari file Excel dengan template pesan yang dapat dikustomisasi.

## Fitur

- ✅ Upload file Excel (.xlsx, .xls)
- ✅ Format otomatis nomor HP ke format 62xxxxxxx
- ✅ Preview data Excel sebelum kirim
- ✅ Template pesan dengan placeholder
- ✅ Mode test (kirim ke 3 nomor pertama saja)
- ✅ Progress tracking real-time
- ✅ Log pengiriman detail
- ✅ Delay antar pesan (dapat diatur)
- ✅ Stop/resume broadcast
- ✅ Responsive design

## Struktur File

```
brodcast_wa_excel/
├── index.html              # Halaman utama
├── script.js              # JavaScript frontend
├── send_message.php       # API untuk kirim pesan WA
├── load_template.php      # Load template dari file
├── view_log.php          # Halaman lihat log
├── clear_log.php         # API hapus log
├── pesan_wa.txt          # Template pesan
├── sampel_kirim_wa.xlsx  # Contoh file Excel
├── broadcast_log.txt     # File log (auto-generated)
└── README.md             # Dokumentasi
```

## Cara Penggunaan

### 1. Setup
- Pastikan server web (Apache/Nginx) dengan PHP sudah berjalan
- Upload semua file ke direktori web server
- Pastikan direktori memiliki permission write untuk file log

### 2. Format File Excel
File Excel harus memiliki kolom untuk:
- **Nomor HP**: bisa bernama `hp`, `HP`, `no_hp`, atau `nomor_hp`
- **Data lain**: sesuai kebutuhan template (ID, Email, Password, Link Zoom, dll)

Contoh struktur Excel:
```
| hp           | id    | email              | password | link_zoom        |
|--------------|-------|--------------------|----------|------------------|
| 0899555079   | BPC01 | <EMAIL>   | pass123  | https://zoom.us/1|
| +62812345678 | BPC02 | <EMAIL>   | pass456  | https://zoom.us/2|
| 8123456789   | BPC03 | <EMAIL>   | pass789  | https://zoom.us/3|
```

### 3. Format Nomor HP
Sistem akan otomatis mengkonversi nomor HP ke format 62xxxxxxx:
- `0899555079` → `62899555079`
- `+62812345678` → `62812345678`
- `8123456789` → `628123456789`
- `899555079` → `62899555079`

### 4. Template Pesan
Gunakan placeholder dalam template pesan yang akan diganti dengan data dari Excel:
- `_hp_` → nomor HP
- `_id_` → ID peserta
- `_email_` → email peserta
- `_password_` → password
- `_link_zoom_` → link zoom
- Dan kolom lainnya sesuai header Excel

### 5. Langkah Penggunaan
1. Buka `index.html` di browser
2. Upload file Excel
3. Preview data untuk memastikan format nomor HP benar
4. Load template pesan atau ketik manual
5. Atur delay antar pesan (default 2 detik)
6. Pilih mode test jika ingin testing ke 3 nomor pertama
7. Klik "Mulai Broadcast"
8. Monitor progress dan log real-time

### 6. Monitoring
- Progress bar menunjukkan persentase selesai
- Counter menampilkan total, berhasil, gagal, dan sedang proses
- Log real-time menampilkan status setiap pengiriman
- Akses `view_log.php` untuk melihat log detail

## Konfigurasi API WhatsApp

Edit file `send_message.php` untuk mengubah konfigurasi API:

```php
$apiUrl = 'http://10.33.8.18/wa-api/api/create-message';
$appKey = '2c561b38-1e6f-45c1-9fec-5e57c2ba04fa';
$authKey = '9hZRSDphKInfG54EGNO9qssTnQq6gtmfLg5rrH43xZEyCak7Ih';
```

## Troubleshooting

### Error "File Excel tidak bisa dibaca"
- Pastikan file berformat .xlsx atau .xls
- Pastikan file tidak corrupt
- Coba save ulang file Excel

### Error "Nomor HP tidak valid"
- Pastikan kolom HP ada di Excel
- Pastikan nomor HP berisi angka
- Sistem akan otomatis format, tapi pastikan nomor valid

### Error "Template tidak bisa dimuat"
- Pastikan file `pesan_wa.txt` ada
- Pastikan file memiliki permission read

### Error "Gagal kirim pesan"
- Cek koneksi internet
- Cek konfigurasi API WhatsApp
- Cek log detail di `view_log.php`

## Keamanan

- Jangan expose API key di frontend
- Gunakan HTTPS untuk production
- Batasi akses file log
- Validasi input file Excel
- Rate limiting untuk mencegah spam

## Pengembangan Lanjutan

Fitur yang bisa ditambahkan:
- [ ] Upload multiple file Excel
- [ ] Scheduled broadcast
- [ ] Template pesan multiple
- [ ] Export report ke Excel
- [ ] Integrasi database
- [ ] User authentication
- [ ] Webhook callback
- [ ] Media attachment support

## Lisensi

Free to use and modify.
