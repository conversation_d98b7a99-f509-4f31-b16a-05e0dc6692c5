<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Log Broadcast WhatsApp</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .log-success { color: #28a745; }
        .log-failed { color: #dc3545; }
        .log-container {
            max-height: 600px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card shadow">
                    <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                        <h3 class="mb-0"><i class="fas fa-list-alt"></i> Log Broadcast WhatsApp</h3>
                        <div>
                            <button class="btn btn-light btn-sm" onclick="location.reload()">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                            <a href="index.html" class="btn btn-primary btn-sm">
                                <i class="fas fa-arrow-left"></i> Kembali
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php
                        $logFile = 'broadcast_log.txt';
                        
                        if (file_exists($logFile)) {
                            $logs = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                            $logs = array_reverse($logs); // Show newest first
                            
                            if (empty($logs)) {
                                echo '<div class="alert alert-info"><i class="fas fa-info-circle"></i> Belum ada log broadcast.</div>';
                            } else {
                                // Statistics
                                $total = count($logs);
                                $success = 0;
                                $failed = 0;
                                
                                foreach ($logs as $log) {
                                    if (strpos($log, 'SUCCESS') !== false) {
                                        $success++;
                                    } else {
                                        $failed++;
                                    }
                                }
                                
                                echo '<div class="row mb-4">';
                                echo '<div class="col-md-4">';
                                echo '<div class="card bg-primary text-white">';
                                echo '<div class="card-body text-center">';
                                echo '<h4>' . $total . '</h4>';
                                echo '<small>Total Pesan</small>';
                                echo '</div></div></div>';
                                
                                echo '<div class="col-md-4">';
                                echo '<div class="card bg-success text-white">';
                                echo '<div class="card-body text-center">';
                                echo '<h4>' . $success . '</h4>';
                                echo '<small>Berhasil</small>';
                                echo '</div></div></div>';
                                
                                echo '<div class="col-md-4">';
                                echo '<div class="card bg-danger text-white">';
                                echo '<div class="card-body text-center">';
                                echo '<h4>' . $failed . '</h4>';
                                echo '<small>Gagal</small>';
                                echo '</div></div></div>';
                                echo '</div>';
                                
                                // Log content
                                echo '<h5>Detail Log:</h5>';
                                echo '<div class="log-container">';
                                
                                foreach ($logs as $log) {
                                    $parts = explode(' | ', $log);
                                    if (count($parts) >= 4) {
                                        $timestamp = $parts[0];
                                        $phone = $parts[1];
                                        $status = $parts[2];
                                        $message = implode(' | ', array_slice($parts, 3));
                                        
                                        $statusClass = ($status === 'SUCCESS') ? 'log-success' : 'log-failed';
                                        $icon = ($status === 'SUCCESS') ? 'check-circle' : 'times-circle';
                                        
                                        echo '<div class="' . $statusClass . '">';
                                        echo '<i class="fas fa-' . $icon . '"></i> ';
                                        echo '<strong>' . $timestamp . '</strong> | ';
                                        echo '<strong>' . $phone . '</strong> | ';
                                        echo '<span class="badge ' . (($status === 'SUCCESS') ? 'bg-success' : 'bg-danger') . '">' . $status . '</span> | ';
                                        echo htmlspecialchars($message);
                                        echo '</div>';
                                    } else {
                                        echo '<div>' . htmlspecialchars($log) . '</div>';
                                    }
                                }
                                
                                echo '</div>';
                                
                                // Clear log button
                                echo '<div class="mt-3">';
                                echo '<button class="btn btn-warning" onclick="clearLog()"><i class="fas fa-trash"></i> Clear Log</button>';
                                echo '</div>';
                            }
                        } else {
                            echo '<div class="alert alert-info"><i class="fas fa-info-circle"></i> File log belum ada. Mulai broadcast untuk membuat log.</div>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function clearLog() {
            if (confirm('Apakah Anda yakin ingin menghapus semua log?')) {
                fetch('clear_log.php', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('Error: ' + error.message);
                });
            }
        }
    </script>
</body>
</html>
