let excelData = [];
let isProcessing = false;
let currentProcessIndex = 0;

// DOM Elements
const uploadArea = document.getElementById('uploadArea');
const excelFile = document.getElementById('excelFile');
const fileInfo = document.getElementById('fileInfo');
const fileName = document.getElementById('fileName');
const previewSection = document.getElementById('previewSection');
const previewTable = document.getElementById('previewTable');
const previewHeader = document.getElementById('previewHeader');
const previewBody = document.getElementById('previewBody');
const messageTemplate = document.getElementById('messageTemplate');
const loadTemplate = document.getElementById('loadTemplate');
const startBroadcast = document.getElementById('startBroadcast');
const stopBroadcast = document.getElementById('stopBroadcast');
const progressContainer = document.querySelector('.progress-container');
const progressBar = document.getElementById('progressBar');
const totalCount = document.getElementById('totalCount');
const successCount = document.getElementById('successCount');
const errorCount = document.getElementById('errorCount');
const currentIndex = document.getElementById('currentIndex');
const logContainer = document.getElementById('logContainer');
const clearLog = document.getElementById('clearLog');
const delaySeconds = document.getElementById('delaySeconds');
const testMode = document.getElementById('testMode');

// Event Listeners
uploadArea.addEventListener('click', () => excelFile.click());
uploadArea.addEventListener('dragover', handleDragOver);
uploadArea.addEventListener('drop', handleDrop);
excelFile.addEventListener('change', handleFileSelect);
loadTemplate.addEventListener('click', loadMessageTemplate);
startBroadcast.addEventListener('click', startBroadcastProcess);
stopBroadcast.addEventListener('click', stopBroadcastProcess);
clearLog.addEventListener('click', () => {
    logContainer.innerHTML = '<div class="log-item log-info"><i class="fas fa-info-circle"></i> Log dibersihkan...</div>';
});

// Drag and Drop handlers
function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
}

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        handleFile(file);
    }
}

function handleFile(file) {
    if (!file.name.match(/\.(xlsx|xls)$/)) {
        addLog('error', 'File harus berformat Excel (.xlsx atau .xls)');
        return;
    }

    fileName.textContent = file.name;
    fileInfo.style.display = 'block';
    
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, {type: 'array'});
            const firstSheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[firstSheetName];
            const jsonData = XLSX.utils.sheet_to_json(worksheet, {header: 1});
            
            processExcelData(jsonData);
            addLog('success', `File ${file.name} berhasil dimuat dengan ${excelData.length} baris data`);
        } catch (error) {
            addLog('error', 'Error membaca file Excel: ' + error.message);
        }
    };
    reader.readAsArrayBuffer(file);
}

function processExcelData(jsonData) {
    if (jsonData.length < 2) {
        addLog('error', 'File Excel harus memiliki minimal 2 baris (header + data)');
        return;
    }

    const headers = jsonData[0];
    excelData = [];

    // Process each row
    for (let i = 1; i < jsonData.length; i++) {
        const row = jsonData[i];
        if (row.length === 0) continue; // Skip empty rows

        const rowData = {};
        headers.forEach((header, index) => {
            rowData[header] = row[index] || '';
        });

        // Format phone number
        if (rowData.hp || rowData.HP || rowData.no_hp || rowData.nomor_hp) {
            const phoneField = rowData.hp || rowData.HP || rowData.no_hp || rowData.nomor_hp;
            rowData.formatted_phone = formatPhoneNumber(phoneField);
        }

        excelData.push(rowData);
    }

    displayPreview(headers);
    startBroadcast.disabled = false;
}

function formatPhoneNumber(phone) {
    if (!phone) return '';
    
    // Remove all non-numeric characters
    let cleaned = phone.toString().replace(/\D/g, '');
    
    // Handle different formats
    if (cleaned.startsWith('62')) {
        return cleaned; // Already in correct format
    } else if (cleaned.startsWith('0')) {
        return '62' + cleaned.substring(1); // Remove leading 0 and add 62
    } else if (cleaned.startsWith('8')) {
        return '62' + cleaned; // Add 62 prefix
    } else {
        return '62' + cleaned; // Default: add 62 prefix
    }
}

function displayPreview(headers) {
    // Show preview section
    previewSection.style.display = 'block';
    
    // Clear previous content
    previewHeader.innerHTML = '';
    previewBody.innerHTML = '';
    
    // Add headers
    headers.forEach(header => {
        const th = document.createElement('th');
        th.textContent = header;
        previewHeader.appendChild(th);
    });
    
    // Add formatted phone column
    const th = document.createElement('th');
    th.textContent = 'Formatted Phone';
    th.className = 'text-success';
    previewHeader.appendChild(th);
    
    // Add first 5 rows as preview
    const previewRows = excelData.slice(0, 5);
    previewRows.forEach(row => {
        const tr = document.createElement('tr');
        
        headers.forEach(header => {
            const td = document.createElement('td');
            td.textContent = row[header] || '';
            tr.appendChild(td);
        });
        
        // Add formatted phone
        const td = document.createElement('td');
        td.textContent = row.formatted_phone || '';
        td.className = 'text-success fw-bold';
        tr.appendChild(td);
        
        previewBody.appendChild(tr);
    });
    
    if (excelData.length > 5) {
        const tr = document.createElement('tr');
        const td = document.createElement('td');
        td.colSpan = headers.length + 1;
        td.textContent = `... dan ${excelData.length - 5} baris lainnya`;
        td.className = 'text-muted text-center';
        tr.appendChild(td);
        previewBody.appendChild(tr);
    }
}

function loadMessageTemplate() {
    fetch('load_template.php')
        .then(response => response.text())
        .then(data => {
            messageTemplate.value = data;
            addLog('success', 'Template pesan berhasil dimuat dari pesan_wa.txt');
        })
        .catch(error => {
            addLog('error', 'Error loading template: ' + error.message);
        });
}

function startBroadcastProcess() {
    if (excelData.length === 0) {
        addLog('error', 'Tidak ada data untuk diproses');
        return;
    }

    if (!messageTemplate.value.trim()) {
        addLog('error', 'Template pesan tidak boleh kosong');
        return;
    }

    isProcessing = true;
    currentProcessIndex = 0;
    
    // Update UI
    startBroadcast.style.display = 'none';
    stopBroadcast.style.display = 'inline-block';
    progressContainer.style.display = 'block';
    
    // Reset counters
    const dataToProcess = testMode.checked ? excelData.slice(0, 3) : excelData;
    totalCount.textContent = dataToProcess.length;
    successCount.textContent = '0';
    errorCount.textContent = '0';
    currentIndex.textContent = '0';
    
    addLog('info', `Memulai broadcast ke ${dataToProcess.length} nomor...`);
    
    // Start processing
    processNextMessage(dataToProcess);
}

function stopBroadcastProcess() {
    isProcessing = false;
    startBroadcast.style.display = 'inline-block';
    stopBroadcast.style.display = 'none';
    addLog('info', 'Broadcast dihentikan oleh user');
}

async function processNextMessage(dataToProcess) {
    if (!isProcessing || currentProcessIndex >= dataToProcess.length) {
        // Finished
        isProcessing = false;
        startBroadcast.style.display = 'inline-block';
        stopBroadcast.style.display = 'none';
        addLog('success', 'Broadcast selesai!');
        return;
    }

    const currentData = dataToProcess[currentProcessIndex];
    currentIndex.textContent = currentProcessIndex + 1;
    
    // Update progress
    const progress = ((currentProcessIndex + 1) / dataToProcess.length) * 100;
    progressBar.style.width = progress + '%';
    progressBar.textContent = Math.round(progress) + '%';
    
    // Prepare message
    let message = messageTemplate.value;
    
    // Replace placeholders
    Object.keys(currentData).forEach(key => {
        const placeholder = `_${key}_`;
        message = message.replace(new RegExp(placeholder, 'g'), currentData[key] || '');
    });
    
    // Send message
    try {
        const response = await fetch('send_message.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                phone: currentData.formatted_phone,
                message: message,
                rowData: currentData
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            successCount.textContent = parseInt(successCount.textContent) + 1;
            addLog('success', `✓ Berhasil kirim ke ${currentData.formatted_phone}`);
        } else {
            errorCount.textContent = parseInt(errorCount.textContent) + 1;
            addLog('error', `✗ Gagal kirim ke ${currentData.formatted_phone}: ${result.message}`);
        }
    } catch (error) {
        errorCount.textContent = parseInt(errorCount.textContent) + 1;
        addLog('error', `✗ Error kirim ke ${currentData.formatted_phone}: ${error.message}`);
    }
    
    currentProcessIndex++;
    
    // Delay before next message
    if (isProcessing && currentProcessIndex < dataToProcess.length) {
        const delay = parseInt(delaySeconds.value) * 1000;
        setTimeout(() => processNextMessage(dataToProcess), delay);
    } else {
        processNextMessage(dataToProcess);
    }
}

function addLog(type, message) {
    const logItem = document.createElement('div');
    logItem.className = `log-item log-${type}`;
    
    const icon = type === 'success' ? 'check-circle' : 
                type === 'error' ? 'exclamation-circle' : 'info-circle';
    
    const timestamp = new Date().toLocaleTimeString();
    logItem.innerHTML = `<i class="fas fa-${icon}"></i> [${timestamp}] ${message}`;
    
    logContainer.appendChild(logItem);
    logContainer.scrollTop = logContainer.scrollHeight;
}
