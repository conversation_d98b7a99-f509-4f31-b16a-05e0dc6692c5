<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Broadcast WhatsApp Excel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            background-color: #e9ecef;
            border-color: #0056b3;
        }
        .upload-area.dragover {
            background-color: #cce5ff;
            border-color: #0056b3;
        }
        .progress-container {
            display: none;
        }
        .log-container {
            max-height: 400px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
        }
        .log-item {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        .log-success {
            background-color: #d4edda;
            color: #155724;
        }
        .log-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .log-info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h3 class="mb-0"><i class="fab fa-whatsapp"></i> Broadcast WhatsApp dari Excel</h3>
                        <a href="view_log.php" class="btn btn-light btn-sm">
                            <i class="fas fa-list-alt"></i> Lihat Log
                        </a>
                    </div>
                    <div class="card-body">
                        <!-- Upload Excel File -->
                        <div class="mb-4">
                            <h5>1. Upload File Excel</h5>
                            <div class="upload-area" id="uploadArea">
                                <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                <h5>Drag & Drop file Excel atau klik untuk browse</h5>
                                <p class="text-muted">Format yang didukung: .xlsx, .xls</p>
                                <input type="file" id="excelFile" accept=".xlsx,.xls" style="display: none;">
                                <button type="button" class="btn btn-primary" onclick="document.getElementById('excelFile').click()">
                                    <i class="fas fa-folder-open"></i> Pilih File
                                </button>
                            </div>
                            <div id="fileInfo" class="mt-3" style="display: none;">
                                <div class="alert alert-info">
                                    <i class="fas fa-file-excel"></i> <span id="fileName"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Preview Data -->
                        <div class="mb-4" id="previewSection" style="display: none;">
                            <h5>2. Preview Data Excel</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-sm" id="previewTable">
                                    <thead class="table-dark">
                                        <tr id="previewHeader"></tr>
                                    </thead>
                                    <tbody id="previewBody"></tbody>
                                </table>
                            </div>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>Catatan:</strong> Nomor HP akan otomatis diformat ke format 62xxxxxxx
                            </div>
                        </div>

                        <!-- Message Template -->
                        <div class="mb-4">
                            <h5>3. Template Pesan</h5>
                            <div class="form-group">
                                <label for="messageTemplate">Pesan Broadcast:</label>
                                <textarea class="form-control" id="messageTemplate" rows="10" placeholder="Masukkan template pesan..."></textarea>
                                <small class="form-text text-muted">
                                    Gunakan placeholder: _ID di Excel_, _Email Di Excel_, _Password Di Excel_, _Link Zoom di Excel_
                                </small>
                            </div>
                            <button type="button" class="btn btn-secondary" id="loadTemplate">
                                <i class="fas fa-file-text"></i> Load dari pesan_wa.txt
                            </button>
                        </div>

                        <!-- Send Controls -->
                        <div class="mb-4">
                            <h5>4. Pengiriman</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="delaySeconds">Delay antar pesan (detik):</label>
                                    <input type="number" class="form-control" id="delaySeconds" value="2" min="1" max="60">
                                </div>
                                <div class="col-md-6">
                                    <label for="testMode">Mode Test:</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="testMode">
                                        <label class="form-check-label" for="testMode">
                                            Kirim hanya ke 3 nomor pertama (untuk testing)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mb-4">
                            <button type="button" class="btn btn-success btn-lg" id="startBroadcast" disabled>
                                <i class="fas fa-paper-plane"></i> Mulai Broadcast
                            </button>
                            <button type="button" class="btn btn-danger btn-lg" id="stopBroadcast" style="display: none;">
                                <i class="fas fa-stop"></i> Stop Broadcast
                            </button>
                        </div>

                        <!-- Progress -->
                        <div class="progress-container">
                            <h5>Progress Pengiriman</h5>
                            <div class="progress mb-3">
                                <div class="progress-bar" role="progressbar" style="width: 0%" id="progressBar">0%</div>
                            </div>
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body">
                                            <h5 id="totalCount">0</h5>
                                            <small>Total</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body">
                                            <h5 id="successCount">0</h5>
                                            <small>Berhasil</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-danger text-white">
                                        <div class="card-body">
                                            <h5 id="errorCount">0</h5>
                                            <small>Gagal</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-info text-white">
                                        <div class="card-body">
                                            <h5 id="currentIndex">0</h5>
                                            <small>Sedang Proses</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Log -->
                        <div class="mt-4">
                            <h5>Log Pengiriman</h5>
                            <div class="log-container" id="logContainer">
                                <div class="log-item log-info">
                                    <i class="fas fa-info-circle"></i> Siap untuk memulai broadcast...
                                </div>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-secondary mt-2" id="clearLog">
                                <i class="fas fa-trash"></i> Clear Log
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
