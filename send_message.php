<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data) {
    echo json_encode(['success' => false, 'message' => 'Invalid JSON data']);
    exit;
}

// Validate required fields
if (empty($data['phone']) || empty($data['message'])) {
    echo json_encode(['success' => false, 'message' => 'Phone and message are required']);
    exit;
}

$phone = $data['phone'];
$message = $data['message'];
$rowData = $data['rowData'] ?? [];

// WhatsApp API configuration
$apiUrl = 'http://10.33.8.18/wa-api/api/create-message';
$appKey = '2c561b38-1e6f-45c1-9fec-5e57c2ba04fa';
$authKey = '9hZRSDphKInfG54EGNO9qssTnQq6gtmfLg5rrH43xZEyCak7Ih';

// Log the attempt
$logEntry = [
    'timestamp' => date('Y-m-d H:i:s'),
    'phone' => $phone,
    'message_length' => strlen($message),
    'row_data' => $rowData
];

// Validate phone number format
if (!preg_match('/^62\d{8,13}$/', $phone)) {
    $response = [
        'success' => false,
        'message' => 'Invalid phone number format. Must be 62xxxxxxxxx',
        'phone' => $phone
    ];
    logMessage($logEntry, $response);
    echo json_encode($response);
    exit;
}

// Prepare cURL request
$curl = curl_init();

curl_setopt_array($curl, array(
    CURLOPT_URL => $apiUrl,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => '',
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => 'POST',
    CURLOPT_POSTFIELDS => array(
        'appkey' => $appKey,
        'authkey' => $authKey,
        'to' => $phone,
        'message' => $message
    ),
    CURLOPT_HTTPHEADER => array(
        'User-Agent: WhatsApp-Broadcast-System/1.0'
    ),
));

// Execute the request
$apiResponse = curl_exec($curl);
$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
$curlError = curl_error($curl);

curl_close($curl);

// Handle cURL errors
if ($curlError) {
    $response = [
        'success' => false,
        'message' => 'cURL Error: ' . $curlError,
        'phone' => $phone
    ];
    logMessage($logEntry, $response);
    echo json_encode($response);
    exit;
}

// Handle HTTP errors
if ($httpCode !== 200) {
    $response = [
        'success' => false,
        'message' => 'HTTP Error: ' . $httpCode,
        'phone' => $phone,
        'api_response' => $apiResponse
    ];
    logMessage($logEntry, $response);
    echo json_encode($response);
    exit;
}

// Try to decode API response
$apiData = json_decode($apiResponse, true);

// Check if API response indicates success
$isSuccess = false;
$apiMessage = 'Unknown response format';

if ($apiData) {
    // Check various possible success indicators
    if (isset($apiData['success']) && $apiData['success']) {
        $isSuccess = true;
        $apiMessage = $apiData['message'] ?? 'Message sent successfully';
    } elseif (isset($apiData['status']) && in_array(strtolower($apiData['status']), ['success', 'sent', 'ok'])) {
        $isSuccess = true;
        $apiMessage = $apiData['message'] ?? 'Message sent successfully';
    } elseif (isset($apiData['error'])) {
        $isSuccess = false;
        $apiMessage = $apiData['error'];
    } elseif (isset($apiData['message'])) {
        // If no clear success/error indicator, assume success if we got a response
        $isSuccess = true;
        $apiMessage = $apiData['message'];
    } else {
        // Default to success if we got a 200 response
        $isSuccess = true;
        $apiMessage = 'Message sent (response: ' . substr($apiResponse, 0, 100) . ')';
    }
} else {
    // If we can't decode JSON but got 200, assume success
    $isSuccess = true;
    $apiMessage = 'Message sent (raw response: ' . substr($apiResponse, 0, 100) . ')';
}

// Prepare response
$response = [
    'success' => $isSuccess,
    'message' => $apiMessage,
    'phone' => $phone,
    'api_response' => $apiData,
    'raw_response' => $apiResponse
];

// Log the result
logMessage($logEntry, $response);

// Return response
echo json_encode($response);

/**
 * Log message attempts and results
 */
function logMessage($logEntry, $response) {
    $logFile = 'broadcast_log.txt';
    $logData = array_merge($logEntry, [
        'success' => $response['success'],
        'response_message' => $response['message']
    ]);
    
    $logLine = date('Y-m-d H:i:s') . ' | ' . 
               $logData['phone'] . ' | ' . 
               ($logData['success'] ? 'SUCCESS' : 'FAILED') . ' | ' . 
               $logData['response_message'] . PHP_EOL;
    
    file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
}
?>
